package com.jcy.underplatformprd.menubc.domain.menuaggr.valueobject;

import com.zz.core.ddd.base.ValueObject;
import com.zz.core.tool.utils.ZzKits;

/**
 * 菜单排序
 *
 * <AUTHOR> <EMAIL><p>
 * ================================<p>
 * Date: 2025-07-16<p>
 * Time: 15:33<p>
 * ================================
 */
public class MenuSort implements ValueObject<MenuSort> {
    /**
     * 菜单排序值
     */
    private final Integer value;

    /**
     * 构造函数
     *
     * @param value 菜单排序
     */
    public MenuSort(Integer value) {
        if (ZzKits.isEmpty(value)) {
            this.value = 0;
        } else {
            this.value = value;
        }
    }

    /**
     * 比较两个值是否相等
     *
     * @param other 另一个对象
     * @return 结果
     */
    @Override
    public boolean sameValueAs(MenuSort other) {
        return this.getValue().equals(other.getValue());
    }

    /**
     * 获取菜单排序值
     *
     * @return 菜单排序值
     */
    public Integer getValue() {
        return value;
    }
}