package com.jcy.underplatformprd.menubc.domain.menuaggr.valueobject;

import com.zz.core.ddd.base.ValueObject;
import com.zz.core.tool.utils.ZzKits;
import com.zz.starter.log.exception.ServiceException;
import com.zz.core.tool.api.ResultCode;

/**
 * 菜单业务编号
 *
 * <AUTHOR> <EMAIL><p>
 * ================================<p>
 * Date: 2025-07-16<p>
 * Time: 15:30<p>
 * ================================
 */
public class MenuSN implements ValueObject<MenuSN> {
    /**
     * 菜单业务编号值
     */
    private final String value;

    /**
     * 构造函数
     *
     * @param value 菜单业务编号
     */
    public MenuSN(String value) {
        if (ZzKits.isEmpty(value)) {
            throw new ServiceException(ResultCode.VALUE_OBJECT_NOT_NULL, "菜单业务编号不能为空");
        }
        this.value = value;
    }

    /**
     * 比较两个值是否相等
     *
     * @param other 另一个对象
     * @return 结果
     */
    @Override
    public boolean sameValueAs(MenuSN other) {
        return this.getValue().equals(other.getValue());
    }

    /**
     * 获取菜单业务编号值
     *
     * @return 菜单业务编号值
     */
    public String getValue() {
        return value;
    }
}