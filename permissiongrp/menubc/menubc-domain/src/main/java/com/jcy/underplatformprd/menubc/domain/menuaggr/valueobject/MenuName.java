package com.jcy.underplatformprd.menubc.domain.menuaggr.valueobject;

import com.zz.core.ddd.base.ValueObject;
import com.zz.core.tool.utils.ZzKits;
import com.zz.starter.log.exception.ServiceException;
import com.zz.core.tool.api.ResultCode;

/**
 * 菜单名称
 *
 * <AUTHOR> <EMAIL><p>
 * ================================<p>
 * Date: 2025-07-16<p>
 * Time: 15:31<p>
 * ================================
 */
public class MenuName implements ValueObject<MenuName> {
    /**
     * 菜单名称值
     */
    private final String value;

    /**
     * 构造函数
     *
     * @param value 菜单名称
     */
    public MenuName(String value) {
        if (ZzKits.isEmpty(value)) {
            throw new ServiceException(ResultCode.VALUE_OBJECT_NOT_NULL, "菜单名称不能为空");
        }
        if (value.length() > 50) {
            throw new ServiceException(ResultCode.VALUE_OBJECT_LENGTH_ERROR, "菜单名称长度不能超过50个字符");
        }
        this.value = value;
    }

    /**
     * 比较两个值是否相等
     *
     * @param other 另一个对象
     * @return 结果
     */
    @Override
    public boolean sameValueAs(MenuName other) {
        return this.getValue().equals(other.getValue());
    }

    /**
     * 获取菜单名称值
     *
     * @return 菜单名称值
     */
    public String getValue() {
        return value;
    }
}