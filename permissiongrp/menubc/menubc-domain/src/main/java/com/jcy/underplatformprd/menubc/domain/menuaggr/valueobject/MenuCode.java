package com.jcy.underplatformprd.menubc.domain.menuaggr.valueobject;

import com.zz.core.ddd.base.ValueObject;
import com.zz.core.tool.utils.ZzKits;
import com.zz.starter.log.exception.ServiceException;
import com.zz.core.tool.api.ResultCode;

/**
 * 菜单编码
 *
 * <AUTHOR> <EMAIL><p>
 * ================================<p>
 * Date: 2025-07-16<p>
 * Time: 15:33<p>
 * ================================
 */
public class MenuCode implements ValueObject<MenuCode> {
    /**
     * 菜单编码值
     */
    private final String value;

    /**
     * 构造函数
     *
     * @param value 菜单编码
     */
    public MenuCode(String value) {
        if (ZzKits.isEmpty(value)) {
            throw new ServiceException(ResultCode.VALUE_OBJECT_NOT_NULL, "菜单编码不能为空");
        }
        if (value.length() > 50) {
            throw new ServiceException(ResultCode.VALUE_OBJECT_LENGTH_ERROR, "菜单编码长度不能超过50个字符");
        }
        this.value = value;
    }

    /**
     * 比较两个值是否相等
     *
     * @param other 另一个对象
     * @return 结果
     */
    @Override
    public boolean sameValueAs(MenuCode other) {
        return this.getValue().equals(other.getValue());
    }

    /**
     * 获取菜单编码值
     *
     * @return 菜单编码值
     */
    public String getValue() {
        return value;
    }
}