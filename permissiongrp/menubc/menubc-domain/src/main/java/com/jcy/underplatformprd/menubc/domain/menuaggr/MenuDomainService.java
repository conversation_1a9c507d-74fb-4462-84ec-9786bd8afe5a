package com.jcy.underplatformprd.menubc.domain.menuaggr;

import com.jcy.underplatformprd.menubc.domain.menuaggr.domainservicevalidator.CreateMenuValidator;
import com.jcy.underplatformprd.menubc.domain.menuaggr.port.MenuCommandRepository;
import com.jcy.underplatformprd.menubc.domain.menuaggr.port.MenuResourceGateway;
import com.jcy.underplatformprd.menubc.domain.menuaggr.valueobject.*;
import com.zz.starter.serialno.template.SerialNoGeneratorTemplate;
import com.zz.starter.log.exception.ServiceException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 菜单领域服务
 *
 * <AUTHOR> <EMAIL><p>
 * ================================<p>
 * Date: 2025-07-16<p>
 * Time: 15:44<p>
 * ================================
 */
@Slf4j
@Service
@AllArgsConstructor
public class MenuDomainService {

    /**
     * 菜单命令仓储
     */
    private final MenuCommandRepository menuCommandRepository;

    /**
     * 菜单资源网关
     */
    private final MenuResourceGateway menuResourceGateway;

    /**
     * 创建菜单
     *
     * @param menuAggregateRootEntity 菜单聚合根
     */
    public void createMenu(MenuAggregateRootEntity menuAggregateRootEntity) {
        log.info("开始创建菜单");
        
        // 1、生成菜单业务编号
        menuAggregateRootEntity.setMenuSN(new MenuSN(SerialNoGeneratorTemplate.get().generateSerialNo()));
        
        // 2、使用校验器进行业务校验
        new CreateMenuValidator(menuResourceGateway).validate(menuAggregateRootEntity);
        
        // 3、设置为新建状态
        menuAggregateRootEntity.toNew();
        
        // 4、存储菜单聚合根
        menuCommandRepository.store(menuAggregateRootEntity);
        
        log.info("菜单创建成功，菜单SN：{}，菜单名称：{}", 
                menuAggregateRootEntity.getMenuSN().getValue(), 
                menuAggregateRootEntity.getMenuName().getValue());
    }

    /**
     * 更新菜单
     *
     * @param menuAggregateRootEntity 菜单聚合根
     */
    public void updateMenu(MenuAggregateRootEntity menuAggregateRootEntity) {
        log.info("开始更新菜单");
        
        // 1、更新菜单信息
        // TODO: 这里需要从仓储中加载现有菜单，然后更新相关字段
        // 当前简化实现，实际场景中需要从仓储查询后更新
        
        // 2、设置为更新状态
        menuAggregateRootEntity.toUpdate();
        
        // 3、存储菜单聚合根
        menuCommandRepository.store(menuAggregateRootEntity);
        
        log.info("菜单更新成功，菜单SN：{}", menuAggregateRootEntity.getMenuSN().getValue());
    }

    /**
     * 删除菜单
     *
     * @param menuAggregateRootEntity 菜单聚合根
     */
    public void deleteMenu(MenuAggregateRootEntity menuAggregateRootEntity) {
        log.info("开始删除菜单");
        
        // 1、检查是否有子菜单
        boolean hasChildren = menuResourceGateway.hasChildren(menuAggregateRootEntity.getMenuSN().getValue());
        if (hasChildren) {
            throw new ServiceException(MenuResultCode.MENU_HAS_CHILDREN);
        }
        
        // 2、设置为删除状态
        menuAggregateRootEntity.toDelete();
        
        // 3、存储菜单聚合根
        menuCommandRepository.store(menuAggregateRootEntity);
        
        log.info("菜单删除成功，菜单SN：{}", menuAggregateRootEntity.getMenuSN().getValue());
    }
}