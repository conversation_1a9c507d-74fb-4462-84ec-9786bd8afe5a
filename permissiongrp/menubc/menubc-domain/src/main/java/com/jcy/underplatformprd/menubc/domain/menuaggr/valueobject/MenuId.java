package com.jcy.underplatformprd.menubc.domain.menuaggr.valueobject;

import com.zz.core.ddd.base.ValueObject;
import com.zz.core.tool.utils.ZzKits;
import com.zz.starter.log.exception.ServiceException;
import com.zz.core.tool.api.ResultCode;

/**
 * 菜单技术Id
 *
 * <AUTHOR> <EMAIL><p>
 * ================================<p>
 * Date: 2025-07-16<p>
 * Time: 15:30<p>
 * ================================
 */
public class MenuId implements ValueObject<MenuId> {
    /**
     * 菜单Id
     */
    private final Long value;

    /**
     * 构造函数
     *
     * @param value 菜单Id
     */
    public MenuId(Long value) {
        if (ZzKits.isEmpty(value)) {
            throw new ServiceException(ResultCode.VALUE_OBJECT_NOT_NULL, "菜单Id不能为空");
        }
        this.value = value;
    }

    /**
     * 比较两个值是否相等
     *
     * @param other 另一个对象
     * @return 结果
     */
    @Override
    public boolean sameValueAs(MenuId other) {
        return this.getValue().equals(other.getValue());
    }

    /**
     * 获取菜单Id值
     *
     * @return 菜单Id值
     */
    public Long getValue() {
        return value;
    }
}