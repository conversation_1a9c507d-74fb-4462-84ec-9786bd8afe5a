package com.jcy.underplatformprd.menubc.domain.menuaggr.valueobject;

import com.zz.core.ddd.base.ValueObject;
import com.zz.core.tool.utils.ZzKits;

/**
 * 菜单备注
 *
 * <AUTHOR> <EMAIL><p>
 * ================================<p>
 * Date: 2025-07-16<p>
 * Time: 15:32<p>
 * ================================
 */
public class MenuRemark implements ValueObject<MenuRemark> {
    /**
     * 菜单备注值
     */
    private final String value;

    /**
     * 构造函数
     *
     * @param value 菜单备注
     */
    public MenuRemark(String value) {
        if (ZzKits.isNotEmpty(value) && value.length() > 500) {
            throw new IllegalArgumentException("菜单备注长度不能超过500个字符");
        }
        this.value = value;
    }

    /**
     * 比较两个值是否相等
     *
     * @param other 另一个对象
     * @return 结果
     */
    @Override
    public boolean sameValueAs(MenuRemark other) {
        if (this.value == null && other.value == null) {
            return true;
        }
        if (this.value == null || other.value == null) {
            return false;
        }
        return this.value.equals(other.value);
    }

    /**
     * 获取菜单备注值
     *
     * @return 菜单备注值
     */
    public String getValue() {
        return value;
    }
}